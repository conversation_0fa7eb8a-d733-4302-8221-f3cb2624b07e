# ===================================
# 构建输出和缓存文件
# ===================================
dist/
.output/
.astro/
build/
out/
.augment/
.env.example
# ===================================
# 依赖包
# ===================================
node_modules/
jspm_packages/

# ===================================
# 日志文件
# ===================================
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.npm

# ===================================
# 环境变量和配置文件
# ===================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
.env.staging

# ===================================
# 部署平台配置
# ===================================
.vercel
.netlify
.firebase
.surge

# ===================================
# IDE 和编辑器配置
# ===================================
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
*.swp
*.swo
*~
.project
.classpath
.settings/

# ===================================
# 操作系统生成的文件
# ===================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride

# Windows
ehthumbs.db
Thumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# ===================================
# 包管理器文件
# ===================================
pnpm-lock.yaml
yarn.lock
package-lock.json
.pnpm-store/

# ===================================
# 运行时数据
# ===================================
pids
*.pid
*.seed
*.pid.lock
.lock-wscript

# ===================================
# 测试覆盖率
# ===================================
coverage/
*.lcov
.nyc_output
.coverage
.cache

# ===================================
# 临时文件和文件夹
# ===================================
tmp/
temp/
.tmp/
.temp/

# ===================================
# 备份文件
# ===================================
*.bak
*.backup
*.old
*.orig

# ===================================
# 压缩文件
# ===================================
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# ===================================
# 数据库文件
# ===================================
*.sqlite
*.sqlite3
*.db

# ===================================
# CI/CD 和自动化工具
# ===================================
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# ===================================
# 其他不需要的文件
# ===================================
.sass-cache/
.stylelintcache
.eslintcache
.parcel-cache/
.next/
.nuxt/
.vuepress/dist
.serverless/
.FuseBox/
.dynamodb/