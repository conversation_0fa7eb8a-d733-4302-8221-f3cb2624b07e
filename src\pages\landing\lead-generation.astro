---
import Layout from '~/layouts/LandingLayout.astro';

import Hero from '~/components/widgets/Hero.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';

const metadata = {
  title: 'Lead Generation Landing Page Demo',
};
---

<Layout metadata={metadata}>
  <!-- Hero2 Widget ******************* -->

  <Hero
    tagline="Lead Generation Landing Demo"
    title="Effective Lead Generation Landing Page: Unlock the Secrets"
    subtitle="Discover the secrets to creating a Landing Page that turns curious visitors into eager leads. (Your Hero should grab attention instantly. Use a powerful headline that speaks directly to your target audience.)"
    actions={[
      { variant: 'primary', text: 'Call to Action', href: '#', icon: 'tabler:square-rounded-arrow-right' },
      { text: 'Learn more', href: '#' },
    ]}
    image={{
      src: 'https://images.unsplash.com/photo-1597423498219-04418210827d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1674&q=80',
      alt: 'Magnet attracting screws. Lead generation landing page demo',
    }}
  />

  <CallToAction
    title="Coming soon"
    subtitle="We are working on the content of these demo pages. You will see them very soon. Stay tuned Stay tuned!"
    actions={[
      {
        variant: 'primary',
        text: 'Download Template',
        href: 'https://github.com/onwidget/astrowind',
        icon: 'tabler:download',
      },
    ]}
  />
</Layout>
