---
import type { Projects as Props } from '~/types';
import Headline from '~/components/ui/Headline.astro';
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import { Icon } from 'astro-icon/components';

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline,
  projects = [],
  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props;
---

<WidgetWrapper id={id} isDark={isDark} containerClass={`max-w-7xl mx-auto ${classes?.container ?? ''}`} bg={bg}>
  <Headline title={title} subtitle={subtitle} tagline={tagline} />
  
  <!-- 项目分类筛选按钮 -->
  <div class="flex flex-wrap justify-center gap-4 mb-8" id="project-filters">
    <button class="filter-btn active px-6 py-2 rounded-full bg-primary text-white font-medium transition-all duration-300 hover:bg-primary-600" data-category="all">
      全部项目
    </button>
    <button class="filter-btn px-6 py-2 rounded-full bg-gray-200 text-gray-700 font-medium transition-all duration-300 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600" data-category="project1">
      玩机工具
    </button>
    <button class="filter-btn px-6 py-2 rounded-full bg-gray-200 text-gray-700 font-medium transition-all duration-300 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600" data-category="project2">
      面具模块
    </button>
    <button class="filter-btn px-6 py-2 rounded-full bg-gray-200 text-gray-700 font-medium transition-all duration-300 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600" data-category="project3">
      小工具
    </button>
  </div>

  <!-- 项目卡片网格 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="projects-grid">
    {projects.map((project) => (
      <div class="project-card bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-700" data-category={project.category}>
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center mr-4">
              <Icon name={`tabler:${project.icon.replace('fa-', '')}`} class="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-1">{project.title}</h3>
              <span class="text-sm text-primary font-medium">{project.platform}</span>
            </div>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
            {project.updateDate}
          </span>
        </div>
        
        <p class="text-gray-600 dark:text-gray-300 mb-4 text-sm leading-relaxed">
          {project.description}
        </p>
        
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 px-3 py-1 rounded-full">
            {project.categoryName}
          </span>
          {project.link !== '#' && (
            <a 
              href={project.link} 
              target="_blank" 
              rel="noopener noreferrer"
              class="inline-flex items-center px-4 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary-600 transition-colors duration-200"
            >
              <Icon name="tabler:external-link" class="w-4 h-4 mr-2" />
              访问项目
            </a>
          )}
        </div>
      </div>
    ))}
  </div>
</WidgetWrapper>

<script>
  // 项目筛选功能
  document.addEventListener('DOMContentLoaded', () => {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const projectCards = document.querySelectorAll('.project-card');

    filterButtons.forEach(button => {
      button.addEventListener('click', () => {
        const category = button.getAttribute('data-category');
        
        // 更新按钮状态
        filterButtons.forEach(btn => {
          btn.classList.remove('active', 'bg-primary', 'text-white');
          btn.classList.add('bg-gray-200', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
        });
        
        button.classList.add('active', 'bg-primary', 'text-white');
        button.classList.remove('bg-gray-200', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
        
        // 筛选项目卡片
        projectCards.forEach(card => {
          const cardCategory = card.getAttribute('data-category');
          if (category === 'all' || cardCategory === category) {
            card.style.display = 'block';
            card.classList.add('animate-fade-in');
          } else {
            card.style.display = 'none';
            card.classList.remove('animate-fade-in');
          }
        });
      });
    });

    // 项目卡片点击事件
    projectCards.forEach(card => {
      card.addEventListener('click', () => {
        const title = card.querySelector('h3')?.textContent || '';
        const description = card.querySelector('p')?.textContent || '';
        const imageUrl = card.querySelector('img')?.src || '';
        const links = card.querySelectorAll('a');

        const actions = Array.from(links).map(link => ({
          text: link.textContent.trim(),
          href: link.href,
          target: link.target,
          icon: link.querySelector('i')?.className || 'fas fa-external-link-alt'
        }));

        // 显示模态框
        if (window.showModal) {
          window.showModal(imageUrl, title, description, actions);
        }
      });
    });
  });
</script>

<style>
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
</style>
