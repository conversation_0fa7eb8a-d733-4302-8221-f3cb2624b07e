---
import Features2 from '~/components/widgets/Features2.astro';
import Features3 from '~/components/widgets/Features3.astro';
import Hero from '~/components/widgets/Hero.astro';
import Stats from '~/components/widgets/Stats.astro';
import Steps2 from '~/components/widgets/Steps2.astro';
import Layout from '~/layouts/PageLayout.astro';

const metadata = {
  title: 'About us',
};
---

<Layout metadata={metadata}>
  <!-- Hero Widget ******************* -->

  <Hero
    tagline="About us"
    image={{
      src: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      alt: 'Caos Image',
    }}
  >
    <Fragment slot="title">
      Elevate your online presence with our <br />
      <span class="text-accent dark:text-white"> Beautiful Website Templates</span>
    </Fragment>

    <Fragment slot="subtitle">
      Donec efficitur, ipsum quis congue luctus, mauris magna convallis mauris, eu auctor nisi lectus non augue. Donec
      quis lorem non massa vulputate efficitur ac at turpis. Sed tincidunt ex a nunc convallis, et lobortis nisi tempus.
      Suspendisse vitae nisi eget tortor luctus maximus sed non lectus.
    </Fragment>
  </Hero>

  <!-- Stats Widget ****************** -->

  <Stats
    title="Statistics about us"
    stats={[
      { title: 'Offices', amount: '4' },
      { title: 'Employees', amount: '248' },
      { title: 'Templates', amount: '12' },
      { title: 'Awards', amount: '24' },
    ]}
  />

  <!-- Features3 Widget ************** -->

  <Features3
    title="Our templates"
    subtitle="Etiam scelerisque, enim eget vestibulum luctus, nibh mauris blandit nulla, nec vestibulum risus justo ut enim. Praesent lacinia diam et ante imperdiet euismod."
    columns={3}
    isBeforeContent={true}
    items={[
      {
        title: 'Educational',
        description:
          'Morbi faucibus luctus quam, sit amet aliquet felis tempor id. Cras augue massa, ornare quis dignissim a, molestie vel nulla.',
        icon: 'tabler:template',
      },
      {
        title: 'Interior Design',
        description:
          'Vivamus porttitor, tortor convallis aliquam pretium, turpis enim consectetur elit, vitae egestas purus erat ac nunc nulla.',
        icon: 'tabler:template',
      },
      {
        title: 'Photography',
        description:
          'Duis sed lectus in nisl vehicula porttitor eget quis odio. Aliquam erat volutpat. Nulla eleifend nulla id sem fermentum.',
        icon: 'tabler:template',
      },
    ]}
  />

  <!-- Features3 Widget ************** -->

  <Features3
    columns={3}
    isAfterContent={true}
    items={[
      {
        title: 'E-commerce',
        description:
          'Rutrum non odio at vehicula. Proin ipsum justo, dignissim in vehicula sit amet, dignissim id quam. Sed ac tincidunt sapien.',
        icon: 'tabler:template',
      },
      {
        title: 'Blog',
        description:
          'Nullam efficitur volutpat sem sed fringilla. Suspendisse et enim eu orci volutpat laoreet ac vitae libero.',
        icon: 'tabler:template',
      },
      {
        title: 'Business',
        description:
          'Morbi et elit finibus, facilisis justo ut, pharetra ipsum. Donec efficitur, ipsum quis congue luctus, mauris magna.',
        icon: 'tabler:template',
      },
      {
        title: 'Branding',
        description:
          'Suspendisse vitae nisi eget tortor luctus maximus sed non lectus. Cras malesuada pretium placerat. Nullam venenatis dolor a ante rhoncus.',
        icon: 'tabler:template',
      },
      {
        title: 'Medical',
        description:
          'Vestibulum malesuada lacus id nibh posuere feugiat. Nam volutpat nulla a felis ultrices, id suscipit mauris congue. In hac habitasse platea dictumst.',
        icon: 'tabler:template',
      },
      {
        title: 'Fashion Design',
        description:
          'Maecenas eu tellus eget est scelerisque lacinia et a diam. Aliquam velit lorem, vehicula id fermentum et, rhoncus et purus.',
        icon: 'tabler:template',
      },
    ]}
    image={{
      src: 'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      alt: 'Colorful Image',
    }}
  />

  <!-- Steps2 Widget ****************** -->

  <Steps2
    title="Our values"
    subtitle="Maecenas eu tellus eget est scelerisque lacinia et a diam. Aliquam velit lorem, vehicula id fermentum et, rhoncus et purus. Nulla facilisi. Vestibulum malesuada lacus."
    items={[
      {
        title: 'Customer-centric approach',
        description:
          'Donec id nibh neque. Quisque et fermentum tortor. Fusce vitae dolor a mauris dignissim commodo. Ut eleifend luctus condimentum.',
      },
      {
        title: 'Constant Improvement',
        description:
          'Phasellus laoreet fermentum venenatis. Vivamus dapibus pulvinar arcu eget mattis. Fusce eget mauris leo.',
      },
      {
        title: 'Ethical Practices',
        description:
          'Vestibulum imperdiet libero et lectus molestie, et maximus augue porta. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.',
      },
    ]}
  />

  <!-- Steps2 Widget ****************** -->

  <Steps2
    title="Achievements"
    subtitle="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi sagittis, quam nec venenatis lobortis, mi risus tempus nulla, sed porttitor est nibh at nulla."
    isReversed={true}
    callToAction={{
      text: 'See more',
      href: '/',
    }}
    items={[
      {
        title: 'Global reach',
        description: 'Nam malesuada urna in enim imperdiet tincidunt. Phasellus non tincidunt nisi, at elementum mi.',
        icon: 'tabler:globe',
      },
      {
        title: 'Positive customer feedback and reviews',
        description:
          'Cras semper nulla leo, eget laoreet erat cursus sed. Praesent faucibus massa in purus iaculis dictum.',
        icon: 'tabler:message-star',
      },
      {
        title: 'Awards and recognition as industry experts',
        description:
          'Phasellus lacinia cursus velit, eu malesuada magna pretium eu. Etiam aliquet tellus purus, blandit lobortis ex rhoncus vitae.',
        icon: 'tabler:award',
      },
    ]}
  />

  <!-- Features2 Widget ************** -->

  <Features2
    title="Our locations"
    tagline="Find us"
    columns={4}
    items={[
      {
        title: 'EE.UU',
        description: '1234 Lorem Ipsum St, 12345, Miami',
      },
      {
        title: 'Spain',
        description: '5678 Lorem Ipsum St, 56789, Madrid',
      },
      {
        title: 'Australia',
        description: '9012 Lorem Ipsum St, 90123, Sydney',
      },
      {
        title: 'Brazil',
        description: '3456 Lorem Ipsum St, 34567, São Paulo',
      },
    ]}
  />

  <!-- Features2 Widget ************** -->

  <Features2
    title="Technical Support"
    tagline="Contact us"
    columns={2}
    items={[
      {
        title: 'Chat with us',
        description:
          'Integer luctus laoreet libero, auctor varius purus rutrum sit amet. Ut nec molestie nisi, quis eleifend mi.',
        icon: 'tabler:messages',
      },
      {
        title: 'Call us',
        description:
          'Mauris faucibus finibus orci, in posuere elit viverra non. In hac habitasse platea dictumst. Cras lobortis metus a hendrerit congue.',
        icon: 'tabler:headset',
      },
    ]}
  />
</Layout>
