---
import Layout from '~/layouts/PageLayout.astro';

import Hero from '~/components/widgets/Hero.astro';
import Stats from '~/components/widgets/Stats.astro';
import Content from '~/components/widgets/Content.astro';
import Projects from '~/components/widgets/Projects.astro';
import Sites from '~/components/widgets/Sites.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import ParticleBackground from '~/components/ParticleBackground.astro';

import Modal from '~/components/Modal.astro';

// 导入图片
import heroImage from '~/assets/images/bg.webp';

// 导入项目数据

const projectsData = [
  {
    id: 1,
    category: "project1",
    categoryName: "玩机工具",
    title: "澎湃解锁工具箱(重构版)",
    description: "一款功能强大的安卓手机工具集合，支持多种系统功能解锁与优化",
    platform: "Windows",
    updateDate: "2025",
    link: "https://hout.lacs.cc/",
    icon: "fa-tools"
  },
  {
    id: 2,
    category: "project1",
    categoryName: "玩机工具",
    title: "小米手机一键Root工具箱（bat版）",
    description: "即将集成在HOUT工具箱中,敬请期待！",
    platform: "Windows",
    updateDate: "2024",
    link: "https://www.coolapk.com/collection/3368316",
    icon: "fa-tools"
  },
  {
    id: 3,
    category: "project2",
    categoryName: "面具模块",
    title: "坤坤模块",
    description: "自定义安卓状态栏，支持多种样式和布局调整，让你的状态栏焕然一新",
    platform: "Magisk",
    updateDate: "2023",
    link: "#",
    icon: "fa-mask"
  },
  {
    id: 4,
    category: "project2",
    categoryName: "面具模块",
    title: "Flyme On Mi模块",
    description: "自定义安卓状态栏，支持多种样式和布局调整，让你的状态栏焕然一新",
    platform: "Magisk",
    updateDate: "2023",
    link: "#",
    icon: "fa-mask"
  },
  {
    id: 5,
    category: "project3",
    categoryName: "小工具",
    title: "单位转换器",
    description: "简单易用的单位转换工具，支持长度、重量、温度等多种单位转换",
    platform: "Web",
    updateDate: "2023-03-15",
    link: "#",
    icon: "fa-calculator"
  }
];

const sitesData = [
  {
    id: 1,
    name: "领创博客",
    description: "分享数码科技、编程开发、设计创意等方面的文章和教程",
    link: "https://blog.lacs.cc/",
    logo: "https://img.lacs.cc/img/25-07/06/lac-blog.webp",
    bgColor: "primary",
    analyticsEvent: "访问领创博客",
    category:"blog"
  },
  {
    id: 2,
    name: "资源商店",
    description: "提供各种高质量的数码资源下载，包括主题、壁纸、字体等",
    link: "https://app.lacs.cc",
    iconContent: "fa-shopping-bag",
    bgColor: "secondary",
    analyticsEvent: "访问资源商店"
  },
  {
    id: 3,
    name: "技术论坛",
    description: "技术交流社区，讨论数码玩机、编程开发、设计创意等话题",
    link: "https://lacs.cc/forum",
    bgColor: "accent",
    analyticsEvent: "访问技术论坛"
  },
  {
    id: 4,
    name: "远程刷机",
    description: "提供专业的远程刷机服务，支持各种Android设备",
    link: "/ycsj",
    bgColor: "dark",
    analyticsEvent: "访问远程刷机"
  }
];

const metadata = {
  title: '领创工作室 - 数码科技与创意内容平台',
  ignoreTitleTemplate: true,
};
---

<Layout metadata={metadata}>
  <!-- Hero Widget ******************* -->

  <Hero
    actions={[
      {
        variant: 'primary',
        text: '探索项目',
        href: '#projects',
        icon: 'tabler:rocket',
      },
      { text: '了解更多', href: '/#about' },
    ]}
    image={{ src: heroImage, alt: '领创工作室团队展示' }}
  >
    <Fragment slot="title">
      <span class="text-white font-bold">领创工作室</span><br>
      <span class="text-white/90 inline-block">Lead And Create Studio</span>
    </Fragment>

    <Fragment slot="bg">
      <ParticleBackground />
    </Fragment>
  </Hero>


  <!-- Projects Widget ****************** -->

  <Projects
    id="projects"
    title="我们的项目作品"
    subtitle="探索领创工作室开发的各类软件工具和创意项目"
    tagline="项目展示"
    projects={projectsData}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-slate-800"></div>
    </Fragment>
  </Projects>



  <!-- Sites Widget ****************** -->

  <Sites
    title="我们的分站平台"
    subtitle="探索领创工作室旗下的各个专业平台"
    tagline="分站导航"
    sites={sitesData}
  />

  <!-- About Widget ******************* -->

  <Content
    id="about"
    title="关于领创工作室"
    subtitle="我们是一个专注于数码科技领域的创新团队"
    tagline="关于我们"
    content="领创工作室成立于2020年，致力于为用户提供高质量的软件工具和技术解决方案。我们的团队由经验丰富的开发者、设计师和技术专家组成，专注于移动设备工具开发、软件开发和数字内容创作。"
    callToAction={{
      variant: 'primary',
      text: '联系我们',
      href: '/contact',
      icon: 'tabler:mail',
    }}
    items={[
      {
        title: '专业团队',
        description: '由经验丰富的开发者和设计师组成',
        icon: 'tabler:users',
      },
      {
        title: '技术创新',
        description: '专注于移动设备工具和软件开发',
        icon: 'tabler:rocket',
      },
      {
        title: '用户至上',
        description: '致力于提供高质量的技术解决方案',
        icon: 'tabler:heart',
      },
    ]}
  />



  <!-- CallToAction Widget *********** -->

  <CallToAction
    actions={[
      {
        variant: 'primary',
        text: '联系我们',
        href: 'mailto:<EMAIL>',
        icon: 'tabler:mail',
      },
      {
        variant: 'secondary',
        text: '加入QQ群',
        href: 'https://qm.qq.com/cgi-bin/qm/qr?k=your_qq_group_key', // TODO: 部署前请更新为真实的QQ群链接
        target: '_blank',
        icon: 'tabler:brand-qq',
      },
    ]}
  >
    <Fragment slot="title">
      加入领创工作室<br class="block sm:hidden" /><span class="sm:whitespace-nowrap">数码科技社区</span>
    </Fragment>

    <Fragment slot="subtitle">
      与我们一起探索数码科技的无限可能，获取最新的技术资讯和工具资源。<br class="hidden md:inline" />
      立即加入我们的社区！
    </Fragment>
  </CallToAction>



  <!-- 模态框 -->
  <Modal />
</Layout>
