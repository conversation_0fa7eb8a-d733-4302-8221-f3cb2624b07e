---
import Headline from '~/components/ui/Headline.astro';
import ItemGrid from '~/components/ui/ItemGrid.astro';
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import type { Faqs as Props } from '~/types';

const {
  title = '',
  subtitle = '',
  tagline = '',
  items = [],
  columns = 2,

  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props;
---

<WidgetWrapper id={id} isDark={isDark} containerClass={`max-w-7xl mx-auto ${classes?.container ?? ''}`} bg={bg}>
  <Headline title={title} subtitle={subtitle} tagline={tagline} />
  <ItemGrid
    items={items}
    columns={columns}
    defaultIcon="tabler:chevron-right"
    classes={{
      container: `${columns === 1 ? 'max-w-4xl' : ''} gap-y-8 md:gap-y-12`,
      panel: 'max-w-none',
      icon: 'flex-shrink-0 mt-1 w-6 h-6 text-primary',
    }}
  />
</WidgetWrapper>
