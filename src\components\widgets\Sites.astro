---
import type { Sites as Props } from '~/types';
import Headline from '~/components/ui/Headline.astro';
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import { Icon } from 'astro-icon/components';

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline,
  sites = [],
  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props;

const getBgColorClass = (bgColor: string) => {
  const colorMap = {
    'primary': 'from-primary to-primary-600',
    'secondary': 'from-secondary to-secondary-600',
    'accent': 'from-accent to-yellow-500',
    'dark': 'from-gray-800 to-gray-900'
  };
  return colorMap[bgColor] || 'from-primary to-primary-600';
};
---

<WidgetWrapper id={id} isDark={isDark} containerClass={`max-w-7xl mx-auto ${classes?.container ?? ''}`} bg={bg}>
  <Headline title={title} subtitle={subtitle} tagline={tagline} />
  
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    {sites.map((site) => (
      <a 
        href={site.link} 
        target="_blank" 
        rel="noopener noreferrer"
        class="site-card group block"
      >
        <div class={`relative overflow-hidden rounded-xl p-6 h-48 bg-gradient-to-br ${getBgColorClass(site.bgColor)} text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2`}>
          <!-- 装饰性背景元素 -->
          <div class="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
          <div class="absolute bottom-0 left-0 w-16 h-16 bg-white/10 rounded-full translate-y-8 -translate-x-8"></div>
          
          <div class="relative z-10 h-full flex flex-col">
            <!-- 图标或Logo -->
            <div class="mb-4">
              {site.logo ? (
                <img src={site.logo} alt={site.name} class="w-12 h-12 rounded-lg object-cover" />
              ) : site.iconContent ? (
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <Icon name={`tabler:${site.iconContent.replace('fa-', '')}`} class="w-6 h-6 text-white" />
                </div>
              ) : (
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <Icon name="tabler:world" class="w-6 h-6 text-white" />
                </div>
              )}
            </div>
            
            <!-- 内容 -->
            <div class="flex-1">
              <h3 class="text-xl font-bold mb-2 group-hover:text-yellow-200 transition-colors duration-200">
                {site.name}
              </h3>
              <p class="text-white/80 text-sm leading-relaxed">
                {site.description}
              </p>
            </div>
            
          
          </div>
        </div>
      </a>
    ))}
  </div>
</WidgetWrapper>

<style>
  .site-card:hover .bg-gradient-to-br {
    background-size: 110% 110%;
  }
</style>
