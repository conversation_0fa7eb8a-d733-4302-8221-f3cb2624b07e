@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .bg-page {
    background-color: var(--aw-color-bg-page);
  }
  .bg-dark {
    background-color: var(--aw-color-bg-page-dark);
  }
  .bg-light {
    background-color: var(--aw-color-bg-page);
  }
  .text-page {
    color: var(--aw-color-text-page);
  }
  .text-muted {
    color: var(--aw-color-text-muted);
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-full border-gray-400 border bg-transparent font-medium text-center text-base text-page leading-snug transition py-3.5 px-6 md:px-8 ease-in duration-200 focus:ring-blue-500 focus:ring-offset-blue-200 focus:ring-2 focus:ring-offset-2 hover:bg-gray-100 hover:border-gray-600 dark:text-slate-300 dark:border-slate-500 dark:hover:bg-slate-800 dark:hover:border-slate-800 cursor-pointer;
  }

  .btn-primary {
    @apply btn font-semibold bg-primary text-white border-primary hover:bg-secondary hover:border-secondary hover:text-white dark:text-white dark:bg-primary dark:border-primary dark:hover:border-secondary dark:hover:bg-secondary;
  }

  .btn-secondary {
    @apply btn;
  }

  .btn-tertiary {
    @apply btn border-none shadow-none text-muted hover:text-gray-900 dark:text-gray-400 dark:hover:text-white;
  }
}

#header.scroll > div:first-child {
  @apply bg-page md:bg-white/90 md:backdrop-blur-md;
  box-shadow: 0 0.375rem 1.5rem 0 rgb(140 152 164 / 13%);
}
.dark #header.scroll > div:first-child,
#header.scroll.dark > div:first-child {
  @apply bg-page md:bg-[#030621e6] border-b border-gray-500/20;
  box-shadow: none;
}
/* #header.scroll > div:last-child {
  @apply py-3;
} */

/* 移动端菜单样式优化 */
#header.expanded nav {
  position: fixed;
  top: 70px;
  left: 8px;
  right: 8px;
  max-height: 60vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 20px;
  z-index: 50;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideDown 0.3s ease-out;
}

.dark #header.expanded nav {
  background: rgba(15, 23, 42, 0.95);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 移动端菜单动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端菜单项样式 */
#header.expanded nav ul {
  gap: 8px;
}

#header.expanded nav ul li {
  margin: 0;
}

#header.expanded nav ul li a {
  display: block;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;
}

#header.expanded nav ul li a:hover {
  background: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
}

.dark #header.expanded nav ul li a:hover {
  background: rgba(59, 130, 246, 0.2);
  color: rgb(147, 197, 253);
}

/* 响应式优化 */
@media (max-width: 640px) {
  #header.expanded nav {
    left: 4px;
    right: 4px;
    max-height: 70vh;
    padding: 16px;
  }
}

@media (orientation: landscape) and (max-height: 600px) {
  #header.expanded nav {
    max-height: 80vh;
  }
}

/* 移动端按钮触摸优化 */
@media (max-width: 768px) {
  /* 确保移动端按钮有足够的触摸目标 */
  button[id*="mobile-"] {
    min-width: 44px;
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* 移动端按钮悬停效果 */
  button[id*="mobile-"]:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* 移动端头部间距优化 */
  #header .flex.items-center.space-x-2 {
    gap: 8px;
  }
}

/* 确保桌面端按钮在移动端隐藏 */
@media (max-width: 767px) {
  button[id*="desktop-"] {
    display: none !important;
  }
}

/* 移动端头部布局优化 */
@media (max-width: 480px) {
  /* 小屏幕设备上的头部间距调整 */
  #header .flex.items-center.gap-1 {
    gap: 4px;
  }

  /* 小屏幕上的按钮尺寸微调 */
  button[id*="mobile-"] {
    min-width: 40px;
    min-height: 40px;
    padding: 8px;
  }

  button[id*="mobile-"] svg {
    width: 18px;
    height: 18px;
  }

  /* 移动端菜单在小屏幕上的调整 */
  #header.expanded nav {
    left: 2px;
    right: 2px;
    padding: 12px;
    max-height: 75vh;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  #header .flex.items-center.gap-1 {
    gap: 2px;
  }

  button[id*="mobile-"] {
    min-width: 36px;
    min-height: 36px;
    padding: 6px;
  }

  button[id*="mobile-"] svg {
    width: 16px;
    height: 16px;
  }
}

/* 横屏模式下的移动端优化 */
@media (orientation: landscape) and (max-width: 768px) {
  #header.expanded nav {
    max-height: 85vh;
    overflow-y: auto;
  }
}

/* 高分辨率移动设备优化 */
@media (-webkit-min-device-pixel-ratio: 2) and (max-width: 768px) {
  button[id*="mobile-"] {
    border: 0.5px solid rgba(0, 0, 0, 0.1);
  }

  .dark button[id*="mobile-"] {
    border: 0.5px solid rgba(255, 255, 255, 0.1);
  }
}

.dropdown:focus .dropdown-menu,
.dropdown:focus-within .dropdown-menu,
.dropdown:hover .dropdown-menu {
  display: block;
}

[astro-icon].icon-light > * {
  stroke-width: 1.2;
}

[astro-icon].icon-bold > * {
  stroke-width: 2.4;
}

[data-aw-toggle-menu] path {
  @apply transition;
}
[data-aw-toggle-menu].expanded g > path:first-child {
  @apply -rotate-45 translate-y-[15px] translate-x-[-3px];
}

[data-aw-toggle-menu].expanded g > path:last-child {
  @apply rotate-45 translate-y-[-8px] translate-x-[14px];
}

/* To deprecated */

.dd *:first-child {
  margin-top: 0;
}
